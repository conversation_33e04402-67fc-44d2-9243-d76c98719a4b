import { useState, useCallback } from 'react';
import { compressImage, needsCompression } from '../../../utils/imageOptimization';
import type { V2Images, V2ImageData } from '../types';

export function useV2ImageManagement() {
  const [v2Images, setV2Images] = useState<V2Images>({
    face: null,
    image_2: null,
    image_3: null,
    image_4: null,
  });
  const [isProcessingImages, setIsProcessingImages] = useState(false);

  const handleImageUpload = useCallback(async (
    file: File, 
    slot: keyof V2Images
  ) => {
    setIsProcessingImages(true);
    try {
      let processedFile = file;
      
      // Compress if needed
      if (needsCompression(file)) {
        processedFile = await compressImage(file);
      }
      
      // Convert to base64
      const reader = new FileReader();
      reader.onloadend = () => {
        const base64 = reader.result as string;
        setV2Images(prev => ({
          ...prev,
          [slot]: {
            file: processedFile,
            base64: base64.split(',')[1], // Remove data URL prefix
            preview: base64,
          },
        }));
      };
      reader.readAsDataURL(processedFile);
    } catch (error) {
      console.error('Error processing image:', error);
    } finally {
      setIsProcessingImages(false);
    }
  }, []);

  const removeImage = useCallback((slot: keyof V2Images) => {
    setV2Images(prev => ({
      ...prev,
      [slot]: null,
    }));
  }, []);

  const clearAllImages = useCallback(() => {
    setV2Images({
      face: null,
      image_2: null,
      image_3: null,
      image_4: null,
    });
  }, []);

  const hasAllRequiredImages = useCallback(() => {
    return !!(
      v2Images.face?.base64 &&
      v2Images.image_2?.base64 &&
      v2Images.image_3?.base64 &&
      v2Images.image_4?.base64
    );
  }, [v2Images]);

  return {
    v2Images,
    isProcessingImages,
    handleImageUpload,
    removeImage,
    clearAllImages,
    hasAllRequiredImages,
  };
}