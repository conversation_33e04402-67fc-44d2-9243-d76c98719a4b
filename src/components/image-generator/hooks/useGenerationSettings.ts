import { useState, useCallback } from 'react';
import type { GenerationSettings } from '../types';

const defaultSettings: GenerationSettings = {
  prompt: '',
  negativePrompt: 'blurry, bad quality, distorted, deformed, ugly, pixelated, low resolution',
  width: 768,
  height: 1024,
  steps: 20,
  cfg: 7,
  seed: -1,
  sampler: 'Euler a',
  clipSkip: 1,
  loraWeight: 1.0,
};

export function useGenerationSettings() {
  const [settings, setSettings] = useState<GenerationSettings>(defaultSettings);
  const [isAdvancedMode, setIsAdvancedMode] = useState(false);

  const updateSetting = useCallback(<K extends keyof GenerationSettings>(
    key: K,
    value: GenerationSettings[K]
  ) => {
    setSettings(prev => ({
      ...prev,
      [key]: value,
    }));
  }, []);

  const resetSettings = useCallback(() => {
    setSettings(defaultSettings);
  }, []);

  const toggleAdvancedMode = useCallback(() => {
    setIsAdvancedMode(prev => !prev);
  }, []);

  return {
    settings,
    isAdvancedMode,
    updateSetting,
    resetSettings,
    toggleAdvancedMode,
  };
}