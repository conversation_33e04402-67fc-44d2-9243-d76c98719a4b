import { useState, useEffect, useMemo } from 'react';
import { useActiveModels } from '../../../hooks/useActiveModels';
import { getModelImageAsBase64 } from '../../../hooks/useModelLibrary';
import { angleBlocks } from '../constants';
import type { Model } from '../types';

export function useModelManagement() {
  const [selectedModels, setSelectedModels] = useState<string[]>([]);
  const [selectedAngles, setSelectedAngles] = useState<string[]>([]);
  const [modelImages, setModelImages] = useState<Record<string, string>>({});
  
  const { models: activeModels = [], isLoading: isLoadingModels } = useActiveModels();

  // Use dynamic models from database
  const campaignModels = useMemo(() => {
    return activeModels.map(model => ({
      id: model.code || model.id,
      name: model.name,
      shortName: model.short_name || model.name,
      description: model.description || '',
      promptText: model.prompt_text || `this ${model.name} fashion model`,
      lora: model.lora_name || '',
      image: model.preview_image || '',
    }));
  }, [activeModels]);

  // Fetch model images
  useEffect(() => {
    const fetchModelImages = async () => {
      const images: Record<string, string> = {};
      
      for (const model of selectedModels) {
        for (const angle of selectedAngles) {
          const key = `${model}-${angle}`;
          
          // Find the model data
          const modelData = activeModels.find(m => m.code === model || m.id === model);
          if (modelData && modelData.imageMapping && modelData.imageMapping[angle]) {
            images[key] = modelData.imageMapping[angle];
          } else {
            // Try to fetch from model library
            try {
              const base64 = await getModelImageAsBase64(model, angle);
              if (base64) {
                images[key] = base64;
              }
            } catch (error) {
              console.error(`Failed to load image for ${model} - ${angle}:`, error);
            }
          }
        }
      }
      
      setModelImages(images);
    };

    if (selectedModels.length > 0 && selectedAngles.length > 0) {
      fetchModelImages();
    }
  }, [selectedModels, selectedAngles, activeModels]);

  const toggleModel = (modelId: string) => {
    setSelectedModels(prev => 
      prev.includes(modelId) 
        ? prev.filter(id => id !== modelId)
        : [...prev, modelId]
    );
  };

  const toggleAngle = (angleName: string) => {
    setSelectedAngles(prev => 
      prev.includes(angleName) 
        ? prev.filter(name => name !== angleName)
        : [...prev, angleName]
    );
  };

  const selectAllModels = () => {
    setSelectedModels(campaignModels.map(m => m.id));
  };

  const selectAllAngles = () => {
    setSelectedAngles(angleBlocks.map(a => a.name));
  };

  const clearAllModels = () => {
    setSelectedModels([]);
  };

  const clearAllAngles = () => {
    setSelectedAngles([]);
  };

  return {
    campaignModels,
    selectedModels,
    selectedAngles,
    modelImages,
    isLoadingModels,
    toggleModel,
    toggleAngle,
    selectAllModels,
    selectAllAngles,
    clearAllModels,
    clearAllAngles,
  };
}