import { useState, useCallback, useEffect } from 'react';
import { useToast } from '../../../components/ui/use-toast';
import { openAIService } from '../../../services/openaiService';
import type { ActiveBlock } from '../PromptBuilder';

export function usePromptBuilder() {
  const [prompt, setPrompt] = useState('');
  const [activeBlocks, setActiveBlocks] = useState<ActiveBlock[]>([]);
  const [isAnalyzingGarment, setIsAnalyzingGarment] = useState(false);
  const { toast } = useToast();

  // Build prompt from active blocks
  const buildPromptFromBlocks = useCallback(() => {
    // Group blocks by type
    const modelBlocks = activeBlocks.filter(b => b.type === 'model');
    const angleBlocks = activeBlocks.filter(b => b.type === 'angle');
    const garmentBlocks = activeBlocks.filter(b => b.type === 'garment');
    const backgroundBlocks = activeBlocks.filter(b => b.type === 'background');
    const artDirectionBlocks = activeBlocks.filter(b => b.type === 'artDirection');

    let promptParts = [];

    // Add model description
    if (modelBlocks.length > 0) {
      promptParts.push(modelBlocks[0].text);
    }

    // Add garments
    if (garmentBlocks.length > 0) {
      promptParts.push(`wearing ${garmentBlocks.map(b => b.text).join(' with ')}`);
    }

    // Add background
    if (backgroundBlocks.length > 0) {
      promptParts.push(backgroundBlocks[0].text);
    }

    // Add angle
    if (angleBlocks.length > 0) {
      promptParts.push(angleBlocks[0].text);
    }

    // Add art direction
    if (artDirectionBlocks.length > 0) {
      promptParts.push(artDirectionBlocks[0].text);
    }

    return promptParts.join(', ');
  }, [activeBlocks]);

  // Update prompt when blocks change
  useEffect(() => {
    const newPrompt = buildPromptFromBlocks();
    setPrompt(newPrompt);
  }, [activeBlocks, buildPromptFromBlocks]);

  // Handle block toggle
  const handleBlockToggle = useCallback((block: ActiveBlock) => {
    setActiveBlocks(prev => {
      const existingIndex = prev.findIndex(b => b.text === block.text);
      
      if (existingIndex >= 0) {
        // Remove block
        return prev.filter((_, index) => index !== existingIndex);
      } else {
        // Add block, but replace if same type
        const filtered = prev.filter(b => b.type !== block.type);
        return [...filtered, block];
      }
    });
  }, []);

  // Add model block
  const addModelBlock = useCallback((model: { name: string; promptText: string }) => {
    handleBlockToggle({
      type: 'model',
      name: model.name,
      text: model.promptText
    });
  }, [handleBlockToggle]);

  // Add angle block
  const addAngleBlock = useCallback((angle: { name: string; promptText: string }) => {
    handleBlockToggle({
      type: 'angle',
      name: angle.name,
      text: angle.promptText
    });
  }, [handleBlockToggle]);

  // Analyze garment with OpenAI
  const analyzeGarment = useCallback(async (imageFile: File, garmentType: string = 'garment') => {
    setIsAnalyzingGarment(true);
    try {
      const result = await openAIService.analyzeGarmentImage(imageFile);
      
      if (result.success && result.description) {
        const garmentBlock: ActiveBlock = {
          type: 'garment',
          name: `${garmentType}: ${result.colors?.[0] || 'Garment'}`,
          text: result.description
        };
        
        // Add garment block
        setActiveBlocks(prev => [...prev, garmentBlock]);
        
        toast({
          title: "Garment analyzed",
          description: "AI description added to prompt",
        });
        
        return result;
      } else {
        throw new Error(result.error || 'Failed to analyze garment');
      }
    } catch (error) {
      console.error('Error analyzing garment:', error);
      toast({
        title: "Analysis failed",
        description: "Could not analyze the garment image",
        variant: "destructive",
      });
      return null;
    } finally {
      setIsAnalyzingGarment(false);
    }
  }, [toast]);

  // Clear all blocks
  const clearBlocks = useCallback(() => {
    setActiveBlocks([]);
    setPrompt('');
  }, []);

  // Manual prompt update
  const updatePrompt = useCallback((newPrompt: string) => {
    setPrompt(newPrompt);
  }, []);

  return {
    prompt,
    activeBlocks,
    isAnalyzingGarment,
    handleBlockToggle,
    addModelBlock,
    addAngleBlock,
    analyzeGarment,
    clearBlocks,
    updatePrompt,
    setActiveBlocks,
  };
}