import { useState, useCallback, useEffect } from 'react';
import { useToast } from '../../../components/ui/use-toast';
import { supabase } from '../../../components/common/utils/supabase';
import type { GeneratedImage } from '../types';

export function useGeneratedImages(collectionId?: string) {
  const [generatedImages, setGeneratedImages] = useState<GeneratedImage[]>([]);
  const [selectedImages, setSelectedImages] = useState<Set<string>>(new Set());
  const [isLoadingImages, setIsLoadingImages] = useState(false);
  const { toast } = useToast();

  // Load existing AI-generated images from database
  useEffect(() => {
    if (collectionId) {
      loadExistingImages();
    }
  }, [collectionId]);

  const loadExistingImages = async () => {
    if (!collectionId) return;
    
    setIsLoadingImages(true);
    try {
      const { data: aiImages, error } = await supabase
        .from('ai_generated_images')
        .select('*')
        .eq('collection_id', collectionId)
        .order('created_at', { ascending: false });

      if (error) throw error;

      if (aiImages && aiImages.length > 0) {
        const formattedImages: GeneratedImage[] = aiImages.map(img => ({
          id: img.id,
          url: img.image_url,
          thumbnail: img.thumbnail_url || img.image_url,
          prompt: img.prompt || '',
          model: img.model_used || 'Unknown',
          modelId: img.model_id || '',
          angle: img.angle || 'Unknown',
          timestamp: new Date(img.created_at),
          metadata: img.metadata || {},
          isFromDatabase: true,
        }));
        
        setGeneratedImages(formattedImages);
      }
    } catch (error) {
      console.error('Error loading AI images:', error);
      toast({
        title: "Error loading images",
        description: "Failed to load existing AI-generated images",
        variant: "destructive",
      });
    } finally {
      setIsLoadingImages(false);
    }
  };

  const addGeneratedImage = useCallback((image: GeneratedImage) => {
    setGeneratedImages(prev => [image, ...prev]);
  }, []);

  const toggleImageSelection = useCallback((imageId: string) => {
    setSelectedImages(prev => {
      const newSet = new Set(prev);
      if (newSet.has(imageId)) {
        newSet.delete(imageId);
      } else {
        newSet.add(imageId);
      }
      return newSet;
    });
  }, []);

  const selectAllImages = useCallback(() => {
    setSelectedImages(new Set(generatedImages.map(img => img.id)));
  }, [generatedImages]);

  const clearSelection = useCallback(() => {
    setSelectedImages(new Set());
  }, []);

  const deleteSelectedImages = useCallback(async () => {
    const imagesToDelete = Array.from(selectedImages);
    
    // Delete from database if they exist there
    const dbImages = generatedImages.filter(
      img => img.isFromDatabase && imagesToDelete.includes(img.id)
    );
    
    if (dbImages.length > 0) {
      const { error } = await supabase
        .from('ai_generated_images')
        .delete()
        .in('id', dbImages.map(img => img.id));
      
      if (error) {
        toast({
          title: "Error deleting images",
          description: "Some images could not be deleted from the database",
          variant: "destructive",
        });
        return;
      }
    }
    
    // Remove from local state
    setGeneratedImages(prev => 
      prev.filter(img => !imagesToDelete.includes(img.id))
    );
    setSelectedImages(new Set());
    
    toast({
      title: "Images deleted",
      description: `${imagesToDelete.length} image(s) deleted successfully`,
    });
  }, [selectedImages, generatedImages, toast]);

  return {
    generatedImages,
    selectedImages,
    isLoadingImages,
    addGeneratedImage,
    toggleImageSelection,
    selectAllImages,
    clearSelection,
    deleteSelectedImages,
    refreshImages: loadExistingImages,
  };
}