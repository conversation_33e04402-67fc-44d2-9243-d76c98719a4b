import React from 'react';
import { Button } from '../../../components/ui/button';
import { 
  CheckSquare, Square, Trash2, Save, 
  Download, ArrowRight, X, Loader2 
} from 'lucide-react';

interface SelectionToolbarProps {
  selectedCount: number;
  totalCount: number;
  onSelectAll: () => void;
  onClearSelection: () => void;
  onDeleteSelected: () => void;
  onMoveToCollection: () => void;
  showMoveToCollection?: boolean;
  isMovingImages?: boolean;
}

export function SelectionToolbar({
  selectedCount,
  totalCount,
  onSelectAll,
  onClearSelection,
  onDeleteSelected,
  onMoveToCollection,
  showMoveToCollection = true,
  isMovingImages = false,
}: SelectionToolbarProps) {
  if (totalCount === 0) return null;

  return (
    <div className="flex items-center justify-between p-4 bg-gray-50 border rounded-lg">
      <div className="flex items-center gap-4">
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={selectedCount === totalCount ? onClearSelection : onSelectAll}
          >
            {selectedCount === totalCount ? (
              <>
                <Square className="h-3 w-3 mr-1" />
                Deselect All
              </>
            ) : (
              <>
                <CheckSquare className="h-3 w-3 mr-1" />
                Select All
              </>
            )}
          </Button>
          
          {selectedCount > 0 && (
            <span className="text-sm text-gray-600">
              {selectedCount} of {totalCount} selected
            </span>
          )}
        </div>
      </div>

      {selectedCount > 0 && (
        <div className="flex items-center gap-2">
          {showMoveToCollection && (
            <Button
              size="sm"
              onClick={onMoveToCollection}
              disabled={isMovingImages}
              className="bg-blue-600 hover:bg-blue-700"
            >
              {isMovingImages ? (
                <>
                  <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                  Moving...
                </>
              ) : (
                <>
                  <ArrowRight className="h-3 w-3 mr-1" />
                  Move to Collection ({selectedCount})
                </>
              )}
            </Button>
          )}
          
          <Button
            variant="outline"
            size="sm"
            onClick={onDeleteSelected}
            className="text-red-600 hover:bg-red-50"
          >
            <Trash2 className="h-3 w-3 mr-1" />
            Delete
          </Button>
        </div>
      )}
    </div>
  );
}