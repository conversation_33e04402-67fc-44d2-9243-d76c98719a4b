import React from 'react';
import { Card, CardContent } from '../../../components/ui/card';
import { Button } from '../../../components/ui/button';
import { Badge } from '../../../components/ui/badge';
import { Checkbox } from '../../../components/ui/checkbox';
import { 
  Maximize2, Download, Trash2, CheckCircle, 
  Loader2, Image as ImageIcon 
} from 'lucide-react';
import { cn } from '../../../components/common/utils/utils';
import type { GeneratedImage } from '../types';

interface GeneratedImagesGridProps {
  images: GeneratedImage[];
  selectedImages: Set<string>;
  onToggleSelection: (imageId: string) => void;
  onViewImage: (image: GeneratedImage) => void;
  onDownloadImage: (image: GeneratedImage) => void;
  isLoading?: boolean;
}

export function GeneratedImagesGrid({
  images,
  selectedImages,
  onToggleSelection,
  onViewImage,
  onDownloadImage,
  isLoading,
}: GeneratedImagesGridProps) {
  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-2 text-gray-400" />
          <p className="text-sm text-gray-500">Loading images...</p>
        </div>
      </div>
    );
  }

  if (images.length === 0) {
    return (
      <div className="flex items-center justify-center h-64 border-2 border-dashed border-gray-200 rounded-lg">
        <div className="text-center">
          <ImageIcon className="h-12 w-12 mx-auto mb-2 text-gray-300" />
          <p className="text-sm text-gray-500">No images generated yet</p>
          <p className="text-xs text-gray-400 mt-1">
            Select models and angles, then click Generate to create images
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
      {images.map((image) => (
        <Card 
          key={image.id}
          className={cn(
            "group relative overflow-hidden transition-all duration-200",
            selectedImages.has(image.id) && "ring-2 ring-blue-500 shadow-lg"
          )}
        >
          <CardContent className="p-0">
            {/* Checkbox overlay */}
            <div className="absolute top-2 left-2 z-10">
              <div className="bg-white rounded-full shadow-md p-1">
                <Checkbox
                  checked={selectedImages.has(image.id)}
                  onCheckedChange={() => onToggleSelection(image.id)}
                  className="h-4 w-4"
                />
              </div>
            </div>

            {/* Image */}
            <div 
              className="aspect-[3/4] bg-gray-100 cursor-pointer relative"
              onClick={() => onViewImage(image)}
            >
              <img
                src={image.thumbnail || image.url}
                alt={`Generated ${image.model} - ${image.angle}`}
                className="w-full h-full object-cover"
                loading="lazy"
              />
              
              {/* Hover overlay */}
              <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                <Maximize2 className="h-8 w-8 text-white" />
              </div>
            </div>

            {/* Image info */}
            <div className="p-3 space-y-2">
              <div className="flex items-center justify-between">
                <Badge variant="secondary" className="text-xs">
                  {image.model}
                </Badge>
                {image.isFromDatabase && (
                  <CheckCircle className="h-3 w-3 text-green-500" title="Saved to database" />
                )}
              </div>
              
              <p className="text-xs text-gray-600 line-clamp-1">
                {image.angle}
              </p>
              
              <p className="text-xs text-gray-400">
                {image.timestamp.toLocaleTimeString()}
              </p>

              {/* Action buttons */}
              <div className="flex gap-1 pt-1">
                <Button
                  size="sm"
                  variant="ghost"
                  className="h-7 w-7 p-0"
                  onClick={(e) => {
                    e.stopPropagation();
                    onDownloadImage(image);
                  }}
                  title="Download"
                >
                  <Download className="h-3 w-3" />
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}