import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>alogTitle } from '../../../components/ui/dialog';
import { Button } from '../../../components/ui/button';
import { Badge } from '../../../components/ui/badge';
import { Download, Copy, X } from 'lucide-react';
import { useToast } from '../../../components/ui/use-toast';
import type { GeneratedImage } from '../types';

interface ImageDetailDialogProps {
  image: GeneratedImage | null;
  isOpen: boolean;
  onClose: () => void;
}

export function ImageDetailDialog({ image, isOpen, onClose }: ImageDetailDialogProps) {
  const { toast } = useToast();

  if (!image) return null;

  const handleDownload = async () => {
    try {
      const response = await fetch(image.url);
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${image.model}_${image.angle}_${image.id}.png`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      
      toast({
        title: "Image downloaded",
        description: "The image has been downloaded to your device",
      });
    } catch (error) {
      toast({
        title: "Download failed",
        description: "Failed to download the image",
        variant: "destructive",
      });
    }
  };

  const handleCopyPrompt = () => {
    navigator.clipboard.writeText(image.prompt);
    toast({
      title: "Prompt copied",
      description: "The prompt has been copied to your clipboard",
    });
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center justify-between">
            <span>Image Details</span>
            <Button
              variant="ghost"
              size="icon"
              onClick={onClose}
              className="h-8 w-8"
            >
              <X className="h-4 w-4" />
            </Button>
          </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-4">
          {/* Image */}
          <div className="relative bg-gray-100 rounded-lg overflow-hidden">
            <img
              src={image.url}
              alt={`${image.model} - ${image.angle}`}
              className="w-full h-auto max-h-[60vh] object-contain"
            />
          </div>

          {/* Image Info */}
          <div className="space-y-3">
            <div className="flex items-center gap-2 flex-wrap">
              <Badge>{image.model}</Badge>
              <Badge variant="secondary">{image.angle}</Badge>
              {image.isFromDatabase && (
                <Badge variant="outline" className="text-green-600">
                  Saved
                </Badge>
              )}
            </div>

            {/* Prompt */}
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <h4 className="text-sm font-medium">Prompt</h4>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleCopyPrompt}
                  className="h-7"
                >
                  <Copy className="h-3 w-3 mr-1" />
                  Copy
                </Button>
              </div>
              <p className="text-sm text-gray-600 bg-gray-50 p-3 rounded">
                {image.prompt}
              </p>
            </div>

            {/* Metadata */}
            {image.metadata && Object.keys(image.metadata).length > 0 && (
              <div className="space-y-2">
                <h4 className="text-sm font-medium">Technical Details</h4>
                <div className="text-sm text-gray-600 bg-gray-50 p-3 rounded space-y-1">
                  {Object.entries(image.metadata).map(([key, value]) => (
                    <div key={key} className="flex justify-between">
                      <span className="font-medium">{key}:</span>
                      <span>{String(value)}</span>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Actions */}
            <div className="flex justify-end gap-2 pt-2">
              <Button
                variant="outline"
                onClick={handleDownload}
              >
                <Download className="h-4 w-4 mr-2" />
                Download
              </Button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}