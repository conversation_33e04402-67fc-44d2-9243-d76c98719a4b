import React, { useState, useCallback, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { Button } from '../../components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../../components/ui/tabs';
import { Progress } from '../../components/ui/progress';
import { useToast } from '../../components/ui/use-toast';
import { Badge } from '../../components/ui/badge';
import { Label } from '../../components/ui/label';
import { Textarea } from '../../components/ui/textarea';
import { 
  ArrowLeft, Sparkles, History, Save, Upload, 
  X, Image as ImageIcon, CheckCircle, Loader2
} from 'lucide-react';
import { cn } from '../../components/common/utils/utils';

// Import hooks
import { useModelManagement } from './hooks/useModelManagement';
import { useGenerationSettings } from './hooks/useGenerationSettings';
import { useV2ImageManagement } from './hooks/useV2ImageManagement';
import { useGeneratedImages } from './hooks/useGeneratedImages';
import { useImageSelection } from './hooks/useImageSelection';
import { usePromptBuilder } from './hooks/usePromptBuilder';
import { useFashionLabImages } from '../../hooks/useFashionLabImages';
import { useCollection } from '../../components/common/hooks/useCollections';
import { useOrganization } from '../../components/common/hooks/useOrganizations';
import { useProducts } from '../../components/common/hooks/useProducts';
import { useAssets } from '../../components/common/hooks/useAssets';
import { getOrCreateDemoCollection } from '../../utils/demoHelpers';
import { openAIService } from '../../services/openaiService';
import { getModelImageAsBase64 } from '../../hooks/useModelLibrary';
import { getAssetUrl } from '../../components/common/utils/utils';

// Import components
import { ModelSelection } from './sidebar/ModelSelection';
import { AngleSelection } from './sidebar/AngleSelection';
import { GenerationSettingsComponent } from './sidebar/GenerationSettings';
import { GeneratedImagesGrid } from './main-content/GeneratedImagesGrid';
import { SelectionToolbar } from './main-content/SelectionToolbar';
import { ImageDetailDialog } from './dialogs/ImageDetailDialog';
import { ProductSelectorDialog } from './dialogs/ProductSelectorDialog';
import { PromptBuilder } from './PromptBuilder';
import { PromptBlocksSelector } from './sidebar/PromptBlocksSelector';
import { FlexibleInputBox, FlexibleInput } from '../image-generator/FlexibleInputBox';

// Import constants and types
import { angleBlocks } from './constants';
import type { GeneratedImage } from './types';

export function ImageGeneratorV2() {
  const navigate = useNavigate();
  const { orgId, collectionId } = useParams();
  const { toast } = useToast();

  // Hooks
  const {
    campaignModels,
    selectedModels,
    selectedAngles,
    modelImages,
    isLoadingModels,
    toggleModel,
    toggleAngle,
    selectAllModels,
    selectAllAngles,
    clearAllModels,
    clearAllAngles,
  } = useModelManagement();

  const {
    settings,
    isAdvancedMode,
    updateSetting,
    resetSettings,
    toggleAdvancedMode,
  } = useGenerationSettings();

  const {
    v2Images,
    isProcessingImages,
    handleImageUpload,
    removeImage,
    clearAllImages,
    hasAllRequiredImages,
  } = useV2ImageManagement();

  const {
    generatedImages,
    selectedImages,
    isLoadingImages,
    addGeneratedImage,
    toggleImageSelection,
    selectAllImages,
    clearSelection,
    deleteSelectedImages,
    refreshImages,
  } = useGeneratedImages(collectionId);

  const {
    moveImagesToCollection,
    isMovingImages,
  } = useImageSelection(collectionId);

  const {
    prompt,
    activeBlocks,
    isAnalyzingGarment,
    handleBlockToggle,
    addModelBlock,
    addAngleBlock,
    analyzeGarment,
    clearBlocks,
    updatePrompt,
    setActiveBlocks,
  } = usePromptBuilder();

  const {
    generateImages: generateV2,
    isGenerating: isGeneratingV2,
    progress: progressV2,
    error: errorV2,
  } = useFashionLabImages({
    collectionId: collectionId || '',
    onComplete: (images) => {
      refreshImages();
    }
  });

  const { data: collection } = useCollection(collectionId!);
  const { data: organization } = useOrganization(orgId!);
  const { data: products = [] } = useProducts(collectionId!);
  const { data: assets = [] } = useAssets(collectionId!);

  // Local state
  const [activeTab, setActiveTab] = useState<'models' | 'v2'>('models');
  const [selectedProduct, setSelectedProduct] = useState<any>(null);
  const [flexibleInputs, setFlexibleInputs] = useState<FlexibleInput[]>([]);
  const [viewingImage, setViewingImage] = useState<GeneratedImage | null>(null);
  const [isImageDetailOpen, setIsImageDetailOpen] = useState(false);
  const [showHistory, setShowHistory] = useState(false);
  const [isProductSelectorOpen, setIsProductSelectorOpen] = useState(false);
  const [showAllModels, setShowAllModels] = useState(false);
  const [showAllAngles, setShowAllAngles] = useState(false);

  // Additional V2 image slots for three-column layout
  const [additionalGarmentImage, setAdditionalGarmentImage] = useState<any>(null);

  // Handle product selection
  const handleProductSelect = useCallback(async (product: any) => {
    setSelectedProduct(product);
    
    // If a product is selected and has an asset, auto-populate the product image slot
    if (product && product.assets && product.assets.length > 0) {
      const primaryAsset = product.assets[0];
      const imageUrl = getAssetUrl(primaryAsset, false); // Get full resolution
      
      try {
        // Fetch the image and convert to base64
        const response = await fetch(imageUrl);
        const blob = await response.blob();
        const reader = new FileReader();
        
        reader.onloadend = () => {
          const base64 = reader.result as string;
          // Update the product image slot (image_3)
          handleImageUpload(new File([blob], product.name, { type: blob.type }), 'image_3');
        };
        
        reader.readAsDataURL(blob);
      } catch (error) {
        console.error('Failed to load product image:', error);
      }
    }
  }, [handleImageUpload]);

  // Generate batch function
  const generateBatch = async () => {
    if (activeTab === 'v2') {
      if (!hasAllRequiredImages()) {
        toast({
          title: "Missing Images",
          description: "Please upload all 4 required images",
          variant: "destructive",
        });
        return;
      }

      try {
        await generateV2({
          prompt: prompt || 'fashion photography',
          faceImage: v2Images.face!.base64!,
          image2: v2Images.image_2!.base64!,
          image3: v2Images.image_3!.base64!,
          image4: v2Images.image_4!.base64!,
          metadata: {
            source: 'image-generator',
            activeBlocks,
            flexibleInputs: flexibleInputs.length > 0 ? flexibleInputs : undefined
          }
        });
      } catch (error) {
        console.error('V2 generation error:', error);
        toast({
          title: "Generation Failed",
          description: "Failed to generate images with V2 API",
          variant: "destructive",
        });
      }
    } else {
      // Model-based generation logic (same as before)
      if (selectedModels.length === 0 || selectedAngles.length === 0) {
        toast({
          title: "Selection Required",
          description: "Please select at least one model and one angle",
          variant: "destructive",
        });
        return;
      }

      let targetCollectionId = collectionId;
      if (!targetCollectionId) {
        targetCollectionId = await getOrCreateDemoCollection();
        if (!targetCollectionId) {
          toast({
            title: "Error",
            description: "Failed to create demo collection",
            variant: "destructive",
          });
          return;
        }
      }

      const combinations = selectedModels.flatMap(modelId => 
        selectedAngles.map(angle => ({ modelId, angle }))
      );

      try {
        for (const { modelId, angle } of combinations) {
          const model = campaignModels.find(m => m.id === modelId);
          if (!model) continue;

          const angleData = angleBlocks.find(a => a.name === angle);
          if (!angleData) continue;

          const modelImageKey = `${modelId}-${angle}`;
          const modelImageBase64 = modelImages[modelImageKey];

          if (!modelImageBase64) {
            console.warn(`No image found for ${modelId} - ${angle}`);
            continue;
          }

          // Use the built prompt from prompt builder or fall back to basic prompt
          const finalPrompt = prompt || `${model.promptText}, ${angleData.promptText}`;

          await generateV2({
            prompt: finalPrompt,
            faceImage: modelImageBase64,
            // Use actual uploaded images if available, otherwise use model image
            image2: v2Images.image_2?.base64 || modelImageBase64,
            image3: v2Images.image_3?.base64 || modelImageBase64,
            image4: v2Images.image_4?.base64 || modelImageBase64,
            metadata: {
              source: 'image-generator',
              modelId,
              modelName: model.name,
              angle: angle,
              loraName: model.lora,
              selectedProduct: selectedProduct?.id,
              selectedModels,
              selectedAngles,
              flexibleInputs: flexibleInputs.length > 0 ? flexibleInputs : undefined
            }
          });
        }
      } catch (error) {
        console.error('Batch generation error:', error);
        toast({
          title: "Generation Failed",
          description: "An error occurred while generating images",
          variant: "destructive",
        });
      }
    }
  };

  const handleMoveToCollection = async () => {
    const selectedImageIds = Array.from(selectedImages);
    
    if (selectedImageIds.length === 0) return;

    const success = await moveImagesToCollection(selectedImageIds);
    
    if (success) {
      clearSelection();
      await refreshImages();
    }
  };

  const handleDownloadImage = async (image: GeneratedImage) => {
    try {
      const response = await fetch(image.url);
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${image.model}_${image.angle}_${image.id}.png`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      toast({
        title: "Download failed",
        description: "Failed to download the image",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b">
        <div className="px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => navigate(`/organizations/${orgId}/collections/${collectionId}`)}
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Collection
              </Button>
              <div>
                <h1 className="text-xl font-semibold">FashionLab Image Generator</h1>
                <div className="flex items-center gap-4 text-sm text-gray-600 mt-1">
                  <span>Campaign: {collection?.name || 'Loading...'}</span>
                  <span>•</span>
                  <span>Products: {products.length}/5</span>
                  <span>•</span>
                  <span>Resolution: 1920x1080 (9:16)</span>
                </div>
              </div>
            </div>
            <div className="flex items-center gap-4 text-sm text-gray-600">
              <span>{campaignModels.length} Models</span>
              <span>×</span>
              <span>{angleBlocks.length} Angles</span>
              <span>=</span>
              <span>{campaignModels.length * angleBlocks.length} images/product</span>
            </div>
          </div>
        </div>
      </div>

      {/* Three Column Layout */}
      <div className="flex h-[calc(100vh-73px)]">
        {/* Left Column - Selection */}
        <div className="w-80 bg-white border-r flex flex-col">
          <div className="p-4 border-b">
            <h2 className="font-medium">Selection</h2>
          </div>
          
          <div className="flex-1 overflow-y-auto p-4 space-y-6">
            {/* Target Garment */}
            <div>
              <Label className="text-sm font-medium mb-2 block">Target Garment</Label>
              <Card className={cn(
                "cursor-pointer transition-all hover:shadow-md",
                selectedProduct && "ring-2 ring-blue-500"
              )}>
                <CardContent className="p-4">
                  {selectedProduct ? (
                    <div className="space-y-2">
                      {selectedProduct.assets && selectedProduct.assets[0] ? (
                        <img 
                          src={getAssetUrl(selectedProduct.assets[0], true)} 
                          alt={selectedProduct.name}
                          className="w-full h-32 object-cover rounded"
                        />
                      ) : (
                        <div className="w-full h-32 bg-gray-100 rounded flex items-center justify-center">
                          <span className="text-gray-400 text-sm">No image</span>
                        </div>
                      )}
                      <p className="text-sm font-medium">{selectedProduct.name}</p>
                      <Button 
                        variant="outline" 
                        size="sm" 
                        className="w-full"
                        onClick={() => setIsProductSelectorOpen(true)}
                      >
                        Change Product
                      </Button>
                    </div>
                  ) : (
                    <div className="text-center py-8 text-gray-500">
                      <p className="text-sm">No product selected</p>
                      <Button 
                        variant="outline" 
                        size="sm" 
                        className="mt-2"
                        onClick={() => setIsProductSelectorOpen(true)}
                      >
                        Select Product
                      </Button>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>

            {/* Additional Garments */}
            <div>
              <Label className="text-sm font-medium mb-2 block">Additional Garments</Label>
              <label className="block">
                <input
                  type="file"
                  accept="image/*"
                  className="hidden"
                  onChange={async (e) => {
                    const file = e.target.files?.[0];
                    if (file) {
                      await analyzeGarment(file, 'Additional Garment');
                    }
                  }}
                  disabled={isAnalyzingGarment}
                />
                <div className="border-2 border-dashed rounded-lg p-8 text-center cursor-pointer hover:border-gray-400 transition-colors">
                  {isAnalyzingGarment ? (
                    <>
                      <Loader2 className="h-8 w-8 mx-auto text-gray-400 mb-2 animate-spin" />
                      <p className="text-sm text-gray-500">Analyzing garment...</p>
                    </>
                  ) : (
                    <>
                      <Upload className="h-8 w-8 mx-auto text-gray-400 mb-2" />
                      <p className="text-sm text-gray-500">Upload & Analyze with AI</p>
                    </>
                  )}
                </div>
              </label>
            </div>

            {/* Models */}
            <div>
              <div className="flex items-center justify-between mb-2">
                <Label className="text-sm font-medium">Models</Label>
                {campaignModels.length > 4 && (
                  <Button 
                    variant="link" 
                    size="sm" 
                    className="h-auto p-0 text-xs"
                    onClick={() => setShowAllModels(!showAllModels)}
                  >
                    {showAllModels ? 'Show less' : `Show all (${campaignModels.length})`}
                  </Button>
                )}
              </div>
              <div className="grid grid-cols-2 gap-2">
                {isLoadingModels ? (
                  <div className="col-span-2 text-center py-4">
                    <Loader2 className="h-6 w-6 animate-spin mx-auto" />
                  </div>
                ) : campaignModels.length === 0 ? (
                  <div className="col-span-2 text-center py-4 text-gray-500">
                    <p className="text-sm">No models available</p>
                  </div>
                ) : (
                  (showAllModels ? campaignModels : campaignModels.slice(0, 4)).map((model) => (
                    <Card 
                      key={model.id}
                      className={cn(
                        "cursor-pointer transition-all hover:shadow-md",
                        selectedModels.includes(model.id) && "ring-2 ring-blue-500"
                      )}
                      onClick={() => {
                        toggleModel(model.id);
                        addModelBlock(model);
                      }}
                    >
                      <CardContent className="p-2">
                        <img 
                          src={getAssetUrl({ 
                            file_path: model.image,
                            thumbnail_path: model.image,
                            compressed_path: model.image
                          }, true)} 
                          alt={model.name}
                          className="w-full h-24 object-cover rounded mb-1"
                        />
                        <p className="text-xs text-center">{model.shortName}</p>
                      </CardContent>
                    </Card>
                  ))
                )}
              </div>
            </div>

            {/* Angles */}
            <div>
              <div className="flex items-center justify-between mb-2">
                <Label className="text-sm font-medium">Angles</Label>
                {angleBlocks.length > 4 && (
                  <Button 
                    variant="link" 
                    size="sm" 
                    className="h-auto p-0 text-xs"
                    onClick={() => setShowAllAngles(!showAllAngles)}
                  >
                    {showAllAngles ? 'Show less' : `Show all (${angleBlocks.length})`}
                  </Button>
                )}
              </div>
              <div className="grid grid-cols-2 gap-2">
                {(showAllAngles ? angleBlocks : angleBlocks.slice(0, 4)).map((angle) => (
                  <Card 
                    key={angle.id}
                    className={cn(
                      "cursor-pointer transition-all hover:shadow-md",
                      selectedAngles.includes(angle.name) && "ring-2 ring-green-500"
                    )}
                    onClick={() => {
                      toggleAngle(angle.name);
                      addAngleBlock(angle);
                    }}
                  >
                    <CardContent className="p-2">
                      <div className="h-16 bg-gray-100 rounded mb-1"></div>
                      <p className="text-xs text-center line-clamp-2">{angle.name}</p>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Middle Column - Generated Images */}
        <div className="flex-1 flex flex-col">
          <div className="p-4 bg-white border-b">
            <div className="flex items-center justify-between">
              <h2 className="font-medium">V2 API Generated Images</h2>
              <SelectionToolbar
                selectedCount={selectedImages.size}
                totalCount={generatedImages.length}
                onSelectAll={selectAllImages}
                onClearSelection={clearSelection}
                onDeleteSelected={deleteSelectedImages}
                onMoveToCollection={handleMoveToCollection}
                showMoveToCollection={!!collectionId}
                isMovingImages={isMovingImages}
              />
            </div>
          </div>
          
          <div className="flex-1 overflow-y-auto p-4">
            <GeneratedImagesGrid
              images={generatedImages}
              selectedImages={selectedImages}
              onToggleSelection={toggleImageSelection}
              onViewImage={(image) => {
                setViewingImage(image);
                setIsImageDetailOpen(true);
              }}
              onDownloadImage={handleDownloadImage}
              isLoading={isLoadingImages}
            />
          </div>
        </div>

        {/* Right Column - Prompt Builder */}
        <div className="w-96 bg-white border-l flex flex-col">
          <div className="p-4 border-b flex items-center justify-between">
            <h2 className="font-medium">Prompt Builder</h2>
            <div className="flex gap-2">
              <Button 
                variant="ghost" 
                size="icon"
                onClick={() => setShowHistory(!showHistory)}
              >
                <History className="h-4 w-4" />
              </Button>
              <Button variant="ghost" size="icon">
                <Save className="h-4 w-4" />
              </Button>
            </div>
          </div>
          
          <div className="flex-1 overflow-y-auto p-4 space-y-4">
            {/* Model Photos Upload */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm">Model Photos</CardTitle>
                <p className="text-xs text-gray-500">Upload photos to customize your model generation</p>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="grid grid-cols-2 gap-3">
                  {/* Model Face Photo */}
                  <div>
                    <Label className="text-xs mb-1 block">Model Face Photo</Label>
                    {v2Images.face ? (
                      <div className="relative group">
                        <img 
                          src={v2Images.face.preview} 
                          alt="Face"
                          className="w-full h-20 object-cover rounded border"
                        />
                        <button
                          onClick={() => removeImage('face')}
                          className="absolute top-1 right-1 bg-red-500 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity"
                        >
                          <X className="w-3 h-3" />
                        </button>
                      </div>
                    ) : (
                      <label className="flex items-center justify-center w-full h-20 border-2 border-dashed border-gray-300 rounded cursor-pointer hover:border-gray-400">
                        <input
                          type="file"
                          accept="image/*"
                          onChange={(e) => {
                            const file = e.target.files?.[0];
                            if (file) handleImageUpload(file, 'face');
                          }}
                          className="hidden"
                        />
                        <div className="text-center">
                          <Upload className="w-4 h-4 text-gray-400 mx-auto mb-1" />
                          <p className="text-xs text-gray-500">Upload Face Photo</p>
                        </div>
                      </label>
                    )}
                  </div>

                  {/* Model Body Shot */}
                  <div>
                    <Label className="text-xs mb-1 block">Model Body Shot</Label>
                    {v2Images.image_2 ? (
                      <div className="relative group">
                        <img 
                          src={v2Images.image_2.preview} 
                          alt="Body"
                          className="w-full h-20 object-cover rounded border"
                        />
                        <button
                          onClick={() => removeImage('image_2')}
                          className="absolute top-1 right-1 bg-red-500 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity"
                        >
                          <X className="w-3 h-3" />
                        </button>
                      </div>
                    ) : (
                      <label className="flex items-center justify-center w-full h-20 border-2 border-dashed border-gray-300 rounded cursor-pointer hover:border-gray-400">
                        <input
                          type="file"
                          accept="image/*"
                          onChange={(e) => {
                            const file = e.target.files?.[0];
                            if (file) handleImageUpload(file, 'image_2');
                          }}
                          className="hidden"
                        />
                        <div className="text-center">
                          <Upload className="w-4 h-4 text-gray-400 mx-auto mb-1" />
                          <p className="text-xs text-gray-500">Model Body Shot</p>
                        </div>
                      </label>
                    )}
                  </div>

                  {/* Product Image */}
                  <div>
                    <Label className="text-xs mb-1 block">Product Image</Label>
                    {v2Images.image_3 ? (
                      <div className="relative group">
                        <img 
                          src={v2Images.image_3.preview} 
                          alt="Product"
                          className="w-full h-20 object-cover rounded border"
                        />
                        <button
                          onClick={() => removeImage('image_3')}
                          className="absolute top-1 right-1 bg-red-500 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity"
                        >
                          <X className="w-3 h-3" />
                        </button>
                      </div>
                    ) : (
                      <label className="flex items-center justify-center w-full h-20 border-2 border-dashed border-gray-300 rounded cursor-pointer hover:border-gray-400">
                        <input
                          type="file"
                          accept="image/*"
                          onChange={(e) => {
                            const file = e.target.files?.[0];
                            if (file) handleImageUpload(file, 'image_3');
                          }}
                          className="hidden"
                        />
                        <div className="text-center">
                          <Upload className="w-4 h-4 text-gray-400 mx-auto mb-1" />
                          <p className="text-xs text-gray-500">Product Image</p>
                        </div>
                      </label>
                    )}
                  </div>

                  {/* Additional Item */}
                  <div>
                    <Label className="text-xs mb-1 block">Additional Item</Label>
                    {v2Images.image_4 ? (
                      <div className="relative group">
                        <img 
                          src={v2Images.image_4.preview} 
                          alt="Additional"
                          className="w-full h-20 object-cover rounded border"
                        />
                        <button
                          onClick={() => removeImage('image_4')}
                          className="absolute top-1 right-1 bg-red-500 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity"
                        >
                          <X className="w-3 h-3" />
                        </button>
                      </div>
                    ) : (
                      <label className="flex items-center justify-center w-full h-20 border-2 border-dashed border-gray-300 rounded cursor-pointer hover:border-gray-400">
                        <input
                          type="file"
                          accept="image/*"
                          onChange={(e) => {
                            const file = e.target.files?.[0];
                            if (file) handleImageUpload(file, 'image_4');
                          }}
                          className="hidden"
                        />
                        <div className="text-center">
                          <Upload className="w-4 h-4 text-gray-400 mx-auto mb-1" />
                          <p className="text-xs text-gray-500">Additional Item</p>
                        </div>
                      </label>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Prompt Builder */}
            <PromptBuilder
              prompt={prompt}
              onPromptChange={updatePrompt}
              activeBlocks={activeBlocks}
              onActiveBlocksChange={setActiveBlocks}
              onShowHistory={() => setShowHistory(!showHistory)}
            />

            {/* Prompt Blocks Selector */}
            <PromptBlocksSelector
              activeBlocks={activeBlocks}
              onBlockToggle={handleBlockToggle}
            />

            {/* Extra Elements */}
            <FlexibleInputBox
              inputs={flexibleInputs}
              onChange={setFlexibleInputs}
            />

            {/* Generate Button */}
            <Button
              className="w-full"
              size="lg"
              onClick={generateBatch}
              disabled={
                isGeneratingV2 || isMovingImages ||
                (activeTab === 'models' && (selectedModels.length === 0 || selectedAngles.length === 0)) ||
                (activeTab === 'v2' && !hasAllRequiredImages())
              }
            >
              <Sparkles className="h-4 w-4 mr-2" />
              {isGeneratingV2 ? 'Generating...' : 'Generate V2 Images'}
            </Button>

            {/* Progress */}
            {isGeneratingV2 && (
              <div className="space-y-1">
                <Progress value={progressV2} className="h-2" />
                <p className="text-xs text-center text-gray-600">
                  Processing API request...
                </p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Image Detail Dialog */}
      <ImageDetailDialog
        image={viewingImage}
        isOpen={isImageDetailOpen}
        onClose={() => {
          setIsImageDetailOpen(false);
          setViewingImage(null);
        }}
      />

      {/* Product Selector Dialog */}
      <ProductSelectorDialog
        isOpen={isProductSelectorOpen}
        onClose={() => setIsProductSelectorOpen(false)}
        collectionId={collectionId || ''}
        selectedProductId={selectedProduct?.id}
        onProductSelect={handleProductSelect}
      />
    </div>
  );
}