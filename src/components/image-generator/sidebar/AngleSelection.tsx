import React from 'react';
import { Card, CardContent } from '../../../components/ui/card';
import { Button } from '../../../components/ui/button';
import { Badge } from '../../../components/ui/badge';
import { cn, getAssetUrl } from '../../../components/common/utils/utils';
import { angleBlocks, anglePreviewMapping } from '../constants';

interface AngleSelectionProps {
  selectedAngles: string[];
  onToggleAngle: (angleName: string) => void;
  onSelectAll: () => void;
  onClearAll: () => void;
}

export function AngleSelection({
  selectedAngles,
  onToggleAngle,
  onSelectAll,
  onClearAll,
}: AngleSelectionProps) {
  return (
    <div className="space-y-3">
      <div className="flex items-center justify-between">
        <h3 className="text-sm font-medium">Select Angles</h3>
        <div className="flex gap-1">
          <Button 
            variant="ghost" 
            size="sm" 
            className="h-7 text-xs"
            onClick={onSelectAll}
          >
            Select All
          </Button>
          <Button 
            variant="ghost" 
            size="sm" 
            className="h-7 text-xs"
            onClick={onClearAll}
          >
            Clear
          </Button>
        </div>
      </div>
      
      <div className="grid grid-cols-2 gap-2">
        {angleBlocks.map((angle) => (
          <Card 
            key={angle.id}
            className={cn(
              "cursor-pointer transition-all duration-200 hover:shadow-md",
              selectedAngles.includes(angle.name) && 
              "ring-2 ring-green-500 shadow-lg transform scale-[1.02]"
            )}
            onClick={() => onToggleAngle(angle.name)}
          >
            <CardContent className="p-2">
              <div className="aspect-[3/4] rounded overflow-hidden mb-2 bg-gray-100">
                {anglePreviewMapping[angle.name] && (
                  <img 
                    src={getAssetUrl({ 
                      file_path: anglePreviewMapping[angle.name],
                      thumbnail_path: anglePreviewMapping[angle.name],
                      compressed_path: anglePreviewMapping[angle.name]
                    }, true)} 
                    alt={angle.name}
                    className="w-full h-full object-cover"
                  />
                )}
              </div>
              <div className="space-y-1">
                <h4 className="text-xs font-medium text-center line-clamp-2">
                  {angle.name}
                </h4>
                {selectedAngles.includes(angle.name) && (
                  <div className="flex justify-center">
                    <Badge variant="secondary" className="text-[10px] h-4">
                      Selected
                    </Badge>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
      
      <div className="text-xs text-gray-500 text-center">
        {selectedAngles.length} angle{selectedAngles.length !== 1 ? 's' : ''} selected
      </div>
    </div>
  );
}