import React from 'react';
import { Upload, X } from 'lucide-react';
import { Label } from '../../../components/ui/label';
import { cn } from '../../../components/common/utils/utils';
import type { V2Images } from '../types';

interface V2ImageUploaderProps {
  images: V2Images;
  onImageUpload: (file: File, slot: keyof V2Images) => void;
  onImageRemove: (slot: keyof V2Images) => void;
  className?: string;
}

export function V2ImageUploader({ 
  images, 
  onImageUpload, 
  onImageRemove, 
  className 
}: V2ImageUploaderProps) {
  const renderImageSlot = (
    slot: keyof V2Images,
    label: string
  ) => {
    const image = images[slot];
    
    return (
      <div className="space-y-1">
        <Label className="text-xs font-medium">{label}</Label>
        <div className="relative">
          {image ? (
            <div className="relative group">
              <img 
                src={image.preview} 
                alt={`${label} preview`}
                className="w-full h-20 object-cover rounded border"
              />
              <button
                onClick={() => onImageRemove(slot)}
                className="absolute top-1 right-1 bg-red-500 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity"
                aria-label={`Remove ${label}`}
              >
                <X className="w-3 h-3" />
              </button>
              <div className="absolute bottom-0 left-0 right-0 bg-black/60 text-white text-[10px] p-1 truncate">
                {image.file?.name || 'Uploaded image'}
              </div>
            </div>
          ) : (
            <label className="flex items-center justify-center w-full h-20 border-2 border-dashed border-gray-300 rounded cursor-pointer hover:border-gray-400 transition-colors">
              <input
                type="file"
                accept="image/*"
                onChange={(e) => {
                  const file = e.target.files?.[0];
                  if (file) onImageUpload(file, slot);
                }}
                className="hidden"
              />
              <div className="text-center">
                <Upload className="w-5 h-5 text-gray-400 mx-auto mb-1" />
                <p className="text-xs text-gray-500">{label}</p>
              </div>
            </label>
          )}
        </div>
      </div>
    );
  };

  const isComplete = !!(images.face && images.image_2 && images.image_3 && images.image_4);

  return (
    <div className={cn("space-y-3", className)}>
      <div>
        <Label className="text-sm font-medium mb-2 block">Fashion Lab V2 API Images</Label>
        <p className="text-xs text-muted-foreground mb-3">
          Upload 4 images: face photo + 3 reference images
        </p>
      </div>
      
      <div className="grid grid-cols-2 gap-2">
        {renderImageSlot('face', 'Face')}
        {renderImageSlot('image_2', 'Garment 1')}
        {renderImageSlot('image_3', 'Garment 2')}
        {renderImageSlot('image_4', 'Garment 3')}
      </div>
      
      {isComplete && (
        <div className="mt-2 p-2 bg-green-50 border border-green-200 rounded">
          <p className="text-xs text-green-700">✓ V2 API ready - all 4 images uploaded</p>
        </div>
      )}
    </div>
  );
}