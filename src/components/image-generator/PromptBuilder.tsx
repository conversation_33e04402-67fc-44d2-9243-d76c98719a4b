import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/card';
import { Textarea } from '../../components/ui/textarea';
import { Badge } from '../../components/ui/badge';
import { X } from 'lucide-react';
import { cn } from '../../components/common/utils/utils';
import { BLOCK_COLORS } from '../../config/imageGeneration.config';

export interface ActiveBlock {
  type: 'model' | 'angle' | 'garment' | 'background' | 'artDirection';
  name: string;
  text: string;
}

interface PromptBuilderProps {
  prompt: string;
  onPromptChange: (prompt: string) => void;
  activeBlocks: ActiveBlock[];
  onActiveBlocksChange: (blocks: ActiveBlock[]) => void;
  onShowHistory?: () => void;
}

export function PromptBuilder({
  prompt,
  onPromptChange,
  activeBlocks,
  onActiveBlocksChange,
  onShowHistory
}: PromptBuilderProps) {
  const removeBlock = (index: number) => {
    const newBlocks = activeBlocks.filter((_, i) => i !== index);
    onActiveBlocksChange(newBlocks);
  };

  return (
    <Card>
      <CardHeader className="pb-3">
        <CardTitle className="text-sm">Prompt</CardTitle>
      </CardHeader>
      <CardContent className="space-y-3">
        {/* Active Blocks */}
        {activeBlocks.length > 0 && (
          <div className="flex flex-wrap gap-2 mb-3">
            {activeBlocks.map((block, index) => {
              const colors = BLOCK_COLORS[block.type as keyof typeof BLOCK_COLORS] || BLOCK_COLORS.background;
              
              return (
                <Badge
                  key={`${block.type}-${index}`}
                  variant="secondary"
                  className={cn(
                    "pr-1 gap-1 text-xs font-normal",
                    colors.bg,
                    colors.text,
                    colors.border
                  )}
                >
                  <span className="max-w-[150px] truncate">{block.name}</span>
                  <button
                    onClick={() => removeBlock(index)}
                    className="ml-1 hover:bg-black/10 rounded p-0.5"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </Badge>
              );
            })}
          </div>
        )}

        {/* Prompt Textarea */}
        <Textarea
          value={prompt}
          onChange={(e) => onPromptChange(e.target.value)}
          placeholder="Describe the image you want to generate..."
          className="min-h-[100px] text-sm"
        />

        {/* Character Count */}
        <div className="flex justify-between items-center text-xs text-gray-500">
          <span>{prompt.length} characters</span>
          {activeBlocks.length > 0 && (
            <span>{activeBlocks.length} blocks active</span>
          )}
        </div>
      </CardContent>
    </Card>
  );
}