import React, { useCallback } from 'react';
import { Check, Maximize2, Sparkles } from 'lucide-react';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { cn } from '../common/utils/utils';
import { Model, AngleBlock } from '../../config/imageGeneration.config';

export interface GeneratedImage {
  id: string;
  url: string;
  modelId?: string;
  modelName: string;
  angleId?: number;
  angleName: string;
  prompt: string;
  seed?: number;
  cfg?: number;
  fluxGuidance?: number;
  imageFormat?: string;
  timestamp: string;
  status: string;
  storage_path?: string;
  created_at?: string;
  selected?: boolean;
}

interface GeneratedImageGridProps {
  images: GeneratedImage[];
  selectedImages: string[];
  onImageSelect: (imageId: string) => void;
  onImageView: (image: GeneratedImage) => void;
  models?: Model[];
  angles?: AngleBlock[];
  className?: string;
  emptyStateMessage?: string;
}

export function GeneratedImageGrid({
  images,
  selectedImages,
  onImageSelect,
  onImageView,
  models = [],
  angles = [],
  className,
  emptyStateMessage = "No images generated yet"
}: GeneratedImageGridProps) {
  const handleImageClick = useCallback((image: GeneratedImage) => {
    onImageView(image);
  }, [onImageView]);

  const handleSelectionClick = useCallback((e: React.MouseEvent, imageId: string) => {
    e.stopPropagation();
    onImageSelect(imageId);
  }, [onImageSelect]);

  // Group images by model if models are provided
  const groupedImages = models.length > 0 ? models.map(model => ({
    model,
    images: images.filter(img => img.modelId === model.id),
    angles
  })) : null;

  const renderImage = (image: GeneratedImage) => {
    const imageUrl = image.storage_path 
      ? `${import.meta.env.VITE_SUPABASE_URL}/storage/v1/object/public/ai-generated/${image.storage_path}`
      : image.url;

    return (
      <div key={image.id} className="relative">
        <div className="relative group">
          <img 
            src={imageUrl}
            alt={`${image.modelName} - ${image.angleName}`}
            className={cn(
              "w-full aspect-[9/16] object-cover rounded-lg cursor-pointer transition-all",
              selectedImages.includes(image.id) && "ring-2 ring-primary"
            )}
            onClick={() => handleImageClick(image)}
          />
          
          {/* Selection checkbox */}
          <div 
            className="absolute top-2 left-2 z-10"
            onClick={(e) => handleSelectionClick(e, image.id)}
          >
            <div className={cn(
              "w-6 h-6 rounded border-2 flex items-center justify-center transition-colors",
              selectedImages.includes(image.id) 
                ? "bg-primary border-primary" 
                : "bg-white/80 border-gray-400 hover:border-gray-600"
            )}>
              {selectedImages.includes(image.id) && (
                <Check className="w-4 h-4 text-white" />
              )}
            </div>
          </div>
          
          {/* Hover overlay */}
          <div className="absolute inset-0 bg-black/0 group-hover:bg-black/40 transition-all rounded-lg flex items-center justify-center opacity-0 group-hover:opacity-100">
            <Button size="icon" variant="secondary" className="h-8 w-8">
              <Maximize2 className="w-4 h-4" />
            </Button>
          </div>
          
          {/* Selected badge */}
          {image.selected && (
            <Badge className="absolute top-2 right-2 bg-green-500">
              Selected
            </Badge>
          )}
        </div>
        
        {/* Image metadata */}
        <p className="text-xs mt-2 text-center">
          {image.created_at 
            ? new Date(image.created_at).toLocaleTimeString()
            : image.angleName
          }
        </p>
      </div>
    );
  };

  // Empty state
  if (images.length === 0) {
    return (
      <div className={cn("flex flex-col items-center justify-center h-96", className)}>
        <div className="w-32 h-32 bg-gray-100 rounded-lg mb-4 flex items-center justify-center">
          <Sparkles className="w-12 h-12 text-gray-400" />
        </div>
        <p className="text-gray-500 text-lg mb-2">{emptyStateMessage}</p>
        <p className="text-gray-400 text-sm">Configure settings and click "Generate Images"</p>
      </div>
    );
  }

  // Grouped view (by model)
  if (groupedImages) {
    return (
      <div className={cn("space-y-6", className)}>
        {groupedImages.map(({ model, images: modelImages, angles: modelAngles }) => (
          modelImages.length > 0 && (
            <div key={model.id}>
              <h4 className="font-medium mb-3 flex items-center gap-2">
                <img src={model.image} className="w-8 h-10 object-cover rounded" alt={model.name} />
                {model.name}
              </h4>
              <div className="grid grid-cols-4 gap-4">
                {modelAngles.map(angle => {
                  const image = modelImages.find(img => img.angleId === angle.id);
                  return (
                    <div key={angle.id} className="relative">
                      {image ? (
                        renderImage(image)
                      ) : (
                        <div className="aspect-[9/16] bg-gray-100 rounded-lg flex items-center justify-center">
                          <p className="text-xs text-gray-400">Pending</p>
                        </div>
                      )}
                      {!image && <p className="text-xs mt-2 text-center">{angle.name}</p>}
                    </div>
                  );
                })}
              </div>
            </div>
          )
        ))}
      </div>
    );
  }

  // Simple grid view
  return (
    <div className={cn("grid grid-cols-4 gap-4", className)}>
      {images.map(renderImage)}
    </div>
  );
}