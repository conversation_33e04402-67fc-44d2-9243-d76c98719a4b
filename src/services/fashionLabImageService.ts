import { supabase } from '../components/common/utils/supabase';

interface GenerateImagesOptions {
  prompt: string;
  faceImage: string; // base64
  image2: string; // base64
  image3: string; // base64
  image4: string; // base64
  collectionId: string;
  storeOnCompletion?: boolean;
  metadata?: Record<string, any>;
}

interface QueueStatusResponse {
  queue_id: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  progress: number;
  images?: string[];
  error?: string;
  stored?: boolean;
}

export class FashionLabImageService {
  /**
   * Generate images using Fashion Lab API V2 (image-based)
   * Uses face image + 3 reference images
   * Returns a queue_id for tracking progress
   */
  static async generateImages(options: GenerateImagesOptions): Promise<{ queue_id: string }> {
    const { data: { session } } = await supabase.auth.getSession();
    if (!session) throw new Error('No active session');

    const response = await fetch(
      `${import.meta.env.VITE_SUPABASE_URL}/functions/v1/generate-images`,
      {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${session.access_token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          prompt: options.prompt,
          face_image: options.faceImage,
          image_2: options.image2,
          image_3: options.image3,
          image_4: options.image4,
          collection_id: options.collectionId,
          store_on_completion: options.storeOnCompletion ?? true,
          metadata: options.metadata,
        }),
      }
    );

    if (!response.ok) {
      const error = await response.text();
      throw new Error(`Failed to generate images: ${error}`);
    }

    const result = await response.json();
    return { queue_id: result.queue_id };
  }

  /**
   * Check generation status and optionally store images
   * If store_images is true, downloads and stores in Supabase Storage
   */
  static async checkQueueStatus(
    queueId: string,
    collectionId?: string,
    storeImages: boolean = true,
    prompt?: string
  ): Promise<QueueStatusResponse> {
    const { data: { session } } = await supabase.auth.getSession();
    if (!session) throw new Error('No active session');

    const response = await fetch(
      `${import.meta.env.VITE_SUPABASE_URL}/functions/v1/queue-status`,
      {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${session.access_token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          queue_id: queueId,
          collection_id: collectionId,
          store_images: storeImages && collectionId ? true : false,
          prompt: prompt,
        }),
      }
    );

    if (!response.ok) {
      const error = await response.text();
      throw new Error(`Failed to check queue status: ${error}`);
    }

    return response.json();
  }

  /**
   * Poll for completion with automatic image storage
   */
  static async waitForCompletion(
    queueId: string,
    collectionId: string,
    options: {
      maxAttempts?: number;
      pollInterval?: number;
      onProgress?: (progress: number) => void;
      prompt?: string;
    } = {}
  ): Promise<QueueStatusResponse> {
    const maxAttempts = options.maxAttempts ?? 60; // 5 minutes max
    const pollInterval = options.pollInterval ?? 5000; // 5 seconds
    
    let attempts = 0;
    
    while (attempts < maxAttempts) {
      const status = await this.checkQueueStatus(queueId, collectionId, true, options.prompt);
      
      if (options.onProgress) {
        options.onProgress(status.progress);
      }
      
      if (status.status === 'completed' || status.status === 'failed') {
        return status;
      }
      
      attempts++;
      await new Promise(resolve => setTimeout(resolve, pollInterval));
    }
    
    throw new Error('Timeout waiting for image generation');
  }

  /**
   * Get AI-generated images for a collection (not yet selected)
   */
  static async getGeneratedImages(collectionId: string, selectedOnly: boolean = false) {
    let query = supabase
      .from('ai_generated_images')
      .select('*')
      .eq('collection_id', collectionId);
    
    if (selectedOnly) {
      query = query.eq('selected', true);
    }
    
    const { data, error } = await query.order('created_at', { ascending: false });
    
    if (error) throw error;
    return data;
  }

  /**
   * Get images by queue ID
   */
  static async getImagesByQueueId(queueId: string) {
    const { data, error } = await supabase
      .from('ai_generated_images')
      .select('*')
      .eq('queue_id', queueId)
      .order('created_at', { ascending: false });
    
    if (error) throw error;
    return data;
  }

  /**
   * Mark AI-generated images as selected and add them to the collection
   */
  static async selectGeneratedImages(imageIds: string[], collectionId: string) {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) throw new Error('No authenticated user');

    // Update ai_generated_images to mark as selected
    const { error: updateError } = await supabase
      .from('ai_generated_images')
      .update({
        selected: true,
        selected_at: new Date().toISOString(),
        selected_by: user.id,
      })
      .in('id', imageIds)
      .eq('collection_id', collectionId);
    
    if (updateError) throw updateError;

    // Get the selected images
    const { data: selectedImages, error: fetchError } = await supabase
      .from('ai_generated_images')
      .select('*')
      .in('id', imageIds);
    
    if (fetchError) throw fetchError;

    // Create asset records for selected images
    const assetPromises = selectedImages.map(async (image) => {
      const { error } = await supabase
        .from('assets')
        .insert({
          collection_id: image.collection_id,
          organization_id: image.organization_id,
          created_by: image.user_id,
          file_name: image.metadata?.file_name || `ai_generated_${image.id}.png`,
          file_type: 'image/png',
          file_path: image.storage_path,
          original_path: image.storage_path,
          compressed_path: image.storage_path, // Will be processed later
          thumbnail_path: image.storage_path, // Will be processed later
          file_size: image.metadata?.file_size || 0,
          tags: ['ai-generated', 'fashion-lab'],
          stage: 'raw_ai_images',
          generation_queue_id: image.queue_id,
          metadata: {
            ...image.metadata,
            ai_generated_image_id: image.id,
            prompt: image.prompt,
          },
        });
      
      if (error) throw error;
    });

    await Promise.all(assetPromises);
    
    return selectedImages;
  }
}