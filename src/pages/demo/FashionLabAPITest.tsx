import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '../../components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/card';
import { Label } from '../../components/ui/label';
import { Textarea } from '../../components/ui/textarea';
import { Alert, AlertDescription } from '../../components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../../components/ui/tabs';
import { Badge } from '../../components/ui/badge';
import { Upload, Send, CheckCircle, XCircle, Loader2, Copy, ArrowLeft } from 'lucide-react';
import { supabase } from '../../components/common/utils/supabase';
import { useNavigate, useParams } from 'react-router-dom';
import { getOrCreateDemoCollection } from '../../utils/demoHelpers';

interface TestResult {
  timestamp: string;
  endpoint: string;
  status: 'success' | 'error' | 'pending';
  message: string;
  data?: any;
  error?: any;
  images?: string[];
}

export default function FashionLabAPITest() {
  const navigate = useNavigate();
  const { collectionId } = useParams<{ collectionId: string }>();
  const [isLoading, setIsLoading] = useState(false);
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [currentCollectionId, setCurrentCollectionId] = useState<string | null>(collectionId || null);
  
  // JWT Test State
  const [jwtToken, setJwtToken] = useState('');
  
  // V2 API Test State
  const [v2Images, setV2Images] = useState<{
    face?: { file: File; preview: string; base64?: string };
    image_2?: { file: File; preview: string; base64?: string };
    image_3?: { file: File; preview: string; base64?: string };
    image_4?: { file: File; preview: string; base64?: string };
  }>({});
  const [v2Prompt, setV2Prompt] = useState('Professional model wearing elegant outfit in studio setting');
  
  // Generated Queue ID
  const [queueId, setQueueId] = useState('');
  
  const addTestResult = (result: Omit<TestResult, 'timestamp'>) => {
    setTestResults(prev => [{
      ...result,
      timestamp: new Date().toISOString()
    }, ...prev]);
  };
  
  // Helper to ensure we have a collection ID
  const ensureCollectionId = async (): Promise<string | null> => {
    if (currentCollectionId) {
      return currentCollectionId;
    }
    
    const newCollectionId = await getOrCreateDemoCollection();
    if (newCollectionId) {
      setCurrentCollectionId(newCollectionId);
      addTestResult({
        endpoint: 'Collection Setup',
        status: 'success',
        message: 'Created demo collection for testing',
        data: { collection_id: newCollectionId }
      });
    } else {
      addTestResult({
        endpoint: 'Collection Setup',
        status: 'error',
        message: 'Failed to create demo collection. Check if you are logged in and have an organization.',
        error: 'No collection ID available'
      });
    }
    return newCollectionId;
  };
  
  // Test Supabase Connection
  const testSupabaseConnection = async () => {
    setIsLoading(true);
    try {
      const { data: { session }, error } = await supabase.auth.getSession();
      
      if (error) throw error;
      
      if (session) {
        addTestResult({
          endpoint: 'Supabase Auth',
          status: 'success',
          message: 'Successfully connected to Supabase',
          data: {
            user_id: session.user.id,
            email: session.user.email,
            expires_at: session.expires_at
          }
        });
      } else {
        throw new Error('No active session');
      }
    } catch (error) {
      addTestResult({
        endpoint: 'Supabase Auth',
        status: 'error',
        message: 'Failed to connect to Supabase',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    } finally {
      setIsLoading(false);
    }
  };
  
  // Test JWT Generation
  const testJWTGeneration = async () => {
    setIsLoading(true);
    try {
      const { data: { session } } = await supabase.auth.getSession();
      if (!session) throw new Error('No active session');
      
      const response = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/fashionlab-jwt`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${session.access_token}`,
          'Content-Type': 'application/json',
        },
      });
      
      if (!response.ok) {
        const error = await response.text();
        throw new Error(`HTTP ${response.status}: ${error}`);
      }
      
      const result = await response.json();
      setJwtToken(result.token);
      
      addTestResult({
        endpoint: 'fashionlab-jwt',
        status: 'success',
        message: 'JWT generated successfully',
        data: {
          token_preview: result.token.substring(0, 50) + '...',
          expires_in: result.expires_in,
          full_token: result.token
        }
      });
    } catch (error) {
      addTestResult({
        endpoint: 'fashionlab-jwt',
        status: 'error',
        message: 'Failed to generate JWT',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    } finally {
      setIsLoading(false);
    }
  };
  
  // Test image access methods
  const testImageAccess = async (imageUrl: string) => {
    setIsLoading(true);
    const results = [];
    
    // Test 1: Direct access (no auth)
    try {
      const response = await fetch(imageUrl, { method: 'HEAD' });
      results.push({
        method: 'Direct (no auth)',
        status: response.status,
        success: response.ok
      });
    } catch (error) {
      results.push({
        method: 'Direct (no auth)',
        status: 0,
        success: false,
        error: error instanceof Error ? error.message : 'Failed'
      });
    }
    
    // Test 2: With API Token
    try {
      const response = await fetch(imageUrl, { 
        method: 'HEAD',
        headers: {
          'Authorization': `Bearer 2ms4LQBtkbvJ8RwFmBht`
        }
      });
      results.push({
        method: 'With API Token',
        status: response.status,
        success: response.ok
      });
    } catch (error) {
      results.push({
        method: 'With API Token',
        status: 0,
        success: false,
        error: error instanceof Error ? error.message : 'Failed'
      });
    }
    
    // Test 3: With JWT
    if (jwtToken) {
      try {
        const response = await fetch(imageUrl, { 
          method: 'HEAD',
          headers: {
            'Authorization': `Bearer ${jwtToken}`
          }
        });
        results.push({
          method: 'With JWT',
          status: response.status,
          success: response.ok
        });
      } catch (error) {
        results.push({
          method: 'With JWT',
          status: 0,
          success: false,
          error: error instanceof Error ? error.message : 'Failed'
        });
      }
    }
    
    // Test 4: Via proxy
    try {
      const proxyUrl = `${import.meta.env.VITE_SUPABASE_URL}/functions/v1/fashion-lab-image-proxy?url=${encodeURIComponent(imageUrl)}`;
      const response = await fetch(proxyUrl, { method: 'HEAD' });
      results.push({
        method: 'Via Proxy',
        status: response.status,
        success: response.ok
      });
    } catch (error) {
      results.push({
        method: 'Via Proxy',
        status: 0,
        success: false,
        error: error instanceof Error ? error.message : 'Failed'
      });
    }
    
    addTestResult({
      endpoint: 'Image Access Test',
      status: results.some(r => r.success) ? 'success' : 'error',
      message: `Tested ${results.length} access methods`,
      data: {
        imageUrl,
        results
      }
    });
    
    setIsLoading(false);
  };
  
  // Handle V2 image upload
  const handleV2ImageUpload = async (type: 'face' | 'image_2' | 'image_3' | 'image_4', file: File) => {
    const preview = URL.createObjectURL(file);
    
    const reader = new FileReader();
    reader.onloadend = () => {
      const base64 = reader.result as string;
      setV2Images(prev => ({
        ...prev,
        [type]: { file, preview, base64 }
      }));
    };
    reader.readAsDataURL(file);
  };
  
  // Test V2 API
  const testV2API = async () => {
    if (!v2Images.face?.base64 || !v2Images.image_2?.base64 || !v2Images.image_3?.base64 || !v2Images.image_4?.base64) {
      addTestResult({
        endpoint: 'generate-images (V2)',
        status: 'error',
        message: 'Please upload all 4 images first',
        error: 'Missing required images'
      });
      return;
    }
    
    // Ensure we have a collection ID
    const collectionId = await ensureCollectionId();
    if (!collectionId) {
      addTestResult({
        endpoint: 'generate-images (V2)',
        status: 'error',
        message: 'No collection ID available. Please ensure you are logged in and have an organization.',
        error: 'Collection setup failed'
      });
      return;
    }
    
    setIsLoading(true);
    try {
      const { data: { session } } = await supabase.auth.getSession();
      if (!session) throw new Error('No active session');
      
      const response = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/generate-images`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${session.access_token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          prompt: v2Prompt,
          collection_id: collectionId,
          face_image: v2Images.face.base64,
          image_2: v2Images.image_2.base64,
          image_3: v2Images.image_3.base64,
          image_4: v2Images.image_4.base64,
          store_on_completion: true,
        }),
      });
      
      if (!response.ok) {
        const error = await response.text();
        throw new Error(`HTTP ${response.status}: ${error}`);
      }
      
      const result = await response.json();
      setQueueId(result.queue_id);
      
      addTestResult({
        endpoint: 'generate-images (V2)',
        status: 'success',
        message: 'V2 API call successful',
        data: result,
        images: result.images
      });
    } catch (error) {
      addTestResult({
        endpoint: 'generate-images (V2)',
        status: 'error',
        message: 'V2 API call failed',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    } finally {
      setIsLoading(false);
    }
  };
  
  // Test Queue Status
  const testQueueStatus = async () => {
    if (!queueId) {
      addTestResult({
        endpoint: 'queue-status',
        status: 'error',
        message: 'No queue ID available',
        error: 'Generate images first to get a queue ID'
      });
      return;
    }
    
    // Ensure we have a collection ID
    const collectionId = await ensureCollectionId();
    if (!collectionId) {
      addTestResult({
        endpoint: 'queue-status',
        status: 'error',
        message: 'No collection ID available. Please ensure you are logged in and have an organization.',
        error: 'Collection setup failed'
      });
      return;
    }
    
    setIsLoading(true);
    try {
      const { data: { session } } = await supabase.auth.getSession();
      if (!session) throw new Error('No active session');
      
      const response = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/queue-status`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${session.access_token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          queue_id: queueId,
          collection_id: collectionId,
          store_images: true,
          prompt: v2Prompt
        }),
      });
      
      if (!response.ok) {
        const error = await response.text();
        throw new Error(`HTTP ${response.status}: ${error}`);
      }
      
      const result = await response.json();
      
      addTestResult({
        endpoint: 'queue-status',
        status: 'success',
        message: `Queue status: ${result.status}`,
        data: result,
        images: result.images
      });
    } catch (error) {
      addTestResult({
        endpoint: 'queue-status',
        status: 'error',
        message: 'Failed to check queue status',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    } finally {
      setIsLoading(false);
    }
  };
  
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };
  
  // Component to display images with authentication
  const ImagePreview = ({ url, jwt }: { url: string; jwt?: string }) => {
    const [imageSrc, setImageSrc] = useState<string>('');
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string>('');
    const [showDirectUrl, setShowDirectUrl] = useState(false);
    
    useEffect(() => {
      const loadImage = async () => {
        try {
          // Check if it's a data URI (base64)
          if (url.startsWith('data:image')) {
            setImageSrc(url);
            setLoading(false);
            return;
          }
          
          // For Fashion Lab images, we know they need authentication
          // Since the proxy has issues, let's show instructions
          if (url.includes('fashionlab.notfirst.rodeo')) {
            setError('Fashion Lab images require authentication. Use the curl command below to download.');
            setShowDirectUrl(true);
            setLoading(false);
            return;
          }
          
          // For other URLs, try loading directly
          const img = new Image();
          img.onload = () => {
            setImageSrc(url);
            setLoading(false);
          };
          img.onerror = () => {
            setError('Failed to load image');
            setLoading(false);
          };
          img.src = url;
        } catch (err) {
          setError('Failed to load image');
          setLoading(false);
        }
      };
      
      loadImage();
    }, [url, jwt]);
    
    if (loading) {
      return (
        <div className="w-full h-48 bg-gray-100 rounded flex items-center justify-center">
          <Loader2 className="w-6 h-6 animate-spin text-gray-400" />
        </div>
      );
    }
    
    if (error) {
      return (
        <div className="w-full bg-red-50 rounded p-4">
          <p className="text-sm text-red-600 mb-2">{error}</p>
          {showDirectUrl && (
            <div className="mt-2 p-2 bg-gray-100 rounded">
              <p className="text-xs font-mono mb-1">Download with curl:</p>
              <code className="text-xs break-all">
                curl -L "{url}" -H "Authorization: Bearer 2ms4LQBtkbvJ8RwFmBht" -o image.png
              </code>
            </div>
          )}
        </div>
      );
    }
    
    return (
      <img 
        src={imageSrc} 
        alt="Generated" 
        className="w-full h-auto rounded border cursor-pointer hover:opacity-90"
        onClick={() => window.open(url, '_blank')}
      />
    );
  };
  
  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-6xl mx-auto">
        <div className="mb-8">
          <Button 
            variant="ghost" 
            onClick={() => navigate(-1)}
            className="mb-4"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back
          </Button>
          <h1 className="text-3xl font-bold mb-2">Fashion Lab API Test Page</h1>
          <p className="text-gray-600">Test API connections and debug integration issues</p>
          {currentCollectionId && (
            <Alert className="mt-4">
              <AlertDescription className="text-sm">
                Using collection ID: <code className="font-mono text-xs bg-gray-100 px-1 rounded">{currentCollectionId}</code>
              </AlertDescription>
            </Alert>
          )}
        </div>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Test Controls */}
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Connection Tests</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <Button 
                  onClick={testSupabaseConnection} 
                  disabled={isLoading}
                  className="w-full"
                >
                  {isLoading ? <Loader2 className="w-4 h-4 mr-2 animate-spin" /> : <CheckCircle className="w-4 h-4 mr-2" />}
                  Test Supabase Connection
                </Button>
                
                <Button 
                  onClick={testJWTGeneration} 
                  disabled={isLoading}
                  className="w-full"
                  variant="secondary"
                >
                  {isLoading ? <Loader2 className="w-4 h-4 mr-2 animate-spin" /> : <Send className="w-4 h-4 mr-2" />}
                  Generate JWT Token
                </Button>
                
                {jwtToken && (
                  <Alert>
                    <AlertDescription className="font-mono text-xs break-all">
                      JWT: {jwtToken.substring(0, 50)}...
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => copyToClipboard(jwtToken)}
                        className="ml-2"
                      >
                        <Copy className="w-3 h-3" />
                      </Button>
                    </AlertDescription>
                  </Alert>
                )}
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <CardTitle>Direct Image Test</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label>Image URL</Label>
                  <input
                    type="text"
                    placeholder="Paste an image URL to test..."
                    className="w-full mt-1 p-2 border rounded"
                    onBlur={(e) => {
                      if (e.target.value) {
                        addTestResult({
                          endpoint: 'Direct Image Test',
                          status: 'success',
                          message: 'Testing image URL',
                          images: [e.target.value]
                        });
                      }
                    }}
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Paste an image URL and click outside to test loading it
                  </p>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <CardTitle>Fashion Lab Image Generation Test</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <Alert>
                  <AlertDescription>
                    Upload a face image + 3 reference images to test the Fashion Lab V2 API.
                    Images will be downloaded and stored in Supabase Storage for secure browser access.
                  </AlertDescription>
                </Alert>
                <div>
                  <Label>Prompt</Label>
                  <Textarea
                    value={v2Prompt}
                    onChange={(e) => setV2Prompt(e.target.value)}
                    placeholder="Enter your prompt..."
                    className="mt-1"
                  />
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  {(['face', 'image_2', 'image_3', 'image_4'] as const).map((type) => (
                    <div key={type} className="space-y-2">
                      <Label className="text-sm">{type === 'face' ? 'Face' : `Input ${type.split('_')[1]}`}</Label>
                      {v2Images[type] ? (
                        <div className="relative">
                          <img 
                            src={v2Images[type]!.preview} 
                            alt={type}
                            className="w-full h-24 object-cover rounded border"
                          />
                          <Badge className="absolute top-1 right-1 text-xs">
                            ✓
                          </Badge>
                        </div>
                      ) : (
                        <label className="flex items-center justify-center w-full h-24 border-2 border-dashed rounded cursor-pointer hover:border-gray-400">
                          <input
                            type="file"
                            accept="image/*"
                            onChange={(e) => {
                              const file = e.target.files?.[0];
                              if (file) handleV2ImageUpload(type, file);
                            }}
                            className="hidden"
                          />
                          <Upload className="w-6 h-6 text-gray-400" />
                        </label>
                      )}
                    </div>
                  ))}
                </div>
                
                <Button 
                  onClick={testV2API} 
                  disabled={isLoading || !v2Images.face || !v2Images.image_2 || !v2Images.image_3 || !v2Images.image_4}
                  className="w-full"
                >
                  {isLoading ? <Loader2 className="w-4 h-4 mr-2 animate-spin" /> : <Send className="w-4 h-4 mr-2" />}
                  Generate Fashion Images
                </Button>
                
                {queueId && (
                  <div className="space-y-2">
                    <Alert>
                      <AlertDescription>
                        Queue ID: {queueId}
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => copyToClipboard(queueId)}
                          className="ml-2"
                        >
                          <Copy className="w-3 h-3" />
                        </Button>
                      </AlertDescription>
                    </Alert>
                    
                    <Button 
                      onClick={testQueueStatus} 
                      disabled={isLoading}
                      className="w-full"
                      variant="outline"
                    >
                      {isLoading ? <Loader2 className="w-4 h-4 mr-2 animate-spin" /> : <CheckCircle className="w-4 h-4 mr-2" />}
                      Check Queue Status
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
          
          {/* Test Results */}
          <div>
            <Card className="h-[800px] flex flex-col">
              <CardHeader>
                <CardTitle>Test Results</CardTitle>
              </CardHeader>
              <CardContent className="flex-1 overflow-auto">
                {testResults.length === 0 ? (
                  <p className="text-gray-500 text-center py-8">No tests run yet</p>
                ) : (
                  <div className="space-y-4">
                    {testResults.map((result, index) => (
                      <div key={index} className="border rounded-lg p-4 space-y-2">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            {result.status === 'success' ? (
                              <CheckCircle className="w-5 h-5 text-green-500" />
                            ) : result.status === 'error' ? (
                              <XCircle className="w-5 h-5 text-red-500" />
                            ) : (
                              <Loader2 className="w-5 h-5 text-blue-500 animate-spin" />
                            )}
                            <span className="font-medium">{result.endpoint}</span>
                          </div>
                          <span className="text-xs text-gray-500">
                            {new Date(result.timestamp).toLocaleTimeString()}
                          </span>
                        </div>
                        
                        <p className={`text-sm ${result.status === 'error' ? 'text-red-600' : 'text-gray-600'}`}>
                          {result.message}
                        </p>
                        
                        {result.data && (
                          <details className="mt-2">
                            <summary className="cursor-pointer text-sm text-blue-600">View Response Data</summary>
                            <pre className="mt-2 p-2 bg-gray-100 rounded text-xs overflow-auto">
                              {JSON.stringify(result.data, null, 2)}
                            </pre>
                          </details>
                        )}
                        
                        {result.error && (
                          <div className="mt-2 p-2 bg-red-50 rounded">
                            <p className="text-sm text-red-700">{result.error}</p>
                          </div>
                        )}
                        
                        {result.images && result.images.length > 0 && (
                          <div className="mt-4">
                            <p className="text-sm font-medium mb-2">Generated Images:</p>
                            <div className="grid grid-cols-2 gap-2">
                              {result.images.map((imageUrl, idx) => (
                                <div key={idx} className="space-y-1">
                                  <ImagePreview url={imageUrl} jwt={jwtToken} />
                                  <div className="flex items-center gap-1">
                                    <input 
                                      type="text" 
                                      value={imageUrl} 
                                      readOnly 
                                      className="text-xs p-1 border rounded flex-1"
                                    />
                                    <Button
                                      size="sm"
                                      variant="ghost"
                                      onClick={() => copyToClipboard(imageUrl)}
                                      className="h-6 w-6 p-0"
                                    >
                                      <Copy className="w-3 h-3" />
                                    </Button>
                                    <Button
                                      size="sm"
                                      variant="outline"
                                      onClick={() => testImageAccess(imageUrl)}
                                      className="h-6 px-2"
                                    >
                                      Test
                                    </Button>
                                  </div>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}