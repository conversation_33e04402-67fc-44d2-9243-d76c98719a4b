import { serve } from 'https://deno.land/std@0.177.0/http/server.ts'
import * as jose from 'https://deno.land/x/jose@v4.13.1/index.ts'
import { corsHeaders } from '../_shared/cors.ts'

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Verify the user is authenticated with Supabase
    const authHeader = req.headers.get('Authorization')
    if (!authHeader?.startsWith('Bearer ')) {
      throw new Error('No authorization header')
    }

    // Get JWT secret from environment
    const jwtSecret = Deno.env.get('FASHIONLAB_JWT_SECRET')
    
    if (!jwtSecret) {
      console.error('FASHIONLAB_JWT_SECRET environment variable is not set')
      throw new Error('JWT service not configured')
    }
    
    // Get user info from Supabase token (optional - for adding claims)
    // You could decode the Supabase JWT here to get user info if needed
    
    // Encode the secret
    const secret = new TextEncoder().encode(jwtSecret)
    
    // Create JWT with claims for Fashion Lab API
    const jwt = await new jose.SignJWT({
      // Add any claims you want to pass to Fashion Lab
      iss: 'fashionlab-app',
      // You could add user ID, organization ID, etc.
      scope: 'image:generate image:refine',
    })
      .setProtectedHeader({ alg: 'HS256' })
      .setIssuedAt()
      .setExpirationTime('1h')
      .sign(secret)

    // Log successful token generation (without exposing the token)
    console.log('JWT token generated successfully for Fashion Lab API access')

    return new Response(
      JSON.stringify({ 
        token: jwt,
        expires_in: 3600, // 1 hour in seconds
        token_type: 'Bearer'
      }),
      { 
        headers: { 
          ...corsHeaders,
          'Content-Type': 'application/json'
        } 
      }
    )
  } catch (error: unknown) {
    console.error('Error generating JWT:', error)
    
    const errorMessage = error instanceof Error ? error.message : 'Failed to generate token'
    
    return new Response(
      JSON.stringify({ error: errorMessage }),
      { 
        status: 401,
        headers: { 
          ...corsHeaders,
          'Content-Type': 'application/json'
        } 
      }
    )
  }
})

/* To invoke locally:

  1. Run `supabase start` (see: https://supabase.com/docs/reference/cli/supabase-start)
  2. Start the function with env vars: `supabase functions serve fashionlab-jwt --env-file .env.local`
  3. Make an HTTP request:

  curl -i --location --request POST 'http://127.0.0.1:54321/functions/v1/fashionlab-jwt' \
    --header 'Authorization: Bearer YOUR_ANON_KEY' \
    --header 'Content-Type: application/json'

  Expected response:
  {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  }

*/