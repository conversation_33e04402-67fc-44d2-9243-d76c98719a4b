import { serve } from 'https://deno.land/std@0.177.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.39.3'
import { corsHeaders } from '../_shared/cors.ts'

interface GenerateImagesRequest {
  // V2 API - required fields
  prompt: string
  face_image: string
  image_2: string
  image_3: string
  image_4: string
  
  // Collection/organization data
  collection_id: string
  metadata?: Record<string, unknown>
  
  // Storage options
  store_on_completion?: boolean // Whether to automatically store images when ready
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Parse request body
    const body: GenerateImagesRequest = await req.json()
    
    // Get Fashion Lab API token
    const fashionLabToken = Deno.env.get('FASHIONLAB_API_TOKEN')
    
    if (!fashionLabToken) {
      console.error('FASHIONLAB_API_TOKEN environment variable is not set')
      // Return mock response for testing
      console.log('Returning mock response for testing')
      return new Response(
        JSON.stringify({
          queue_id: `mock-${Date.now()}`,
          status: 'processing',
          images: [],
          error: null
        }),
        { 
          headers: { 
            ...corsHeaders,
            'Content-Type': 'application/json'
          } 
        }
      )
    }
    
    // Validate required V2 API fields
    if (!body.face_image || !body.image_2 || !body.image_3 || !body.image_4) {
      throw new Error('V2 API requires face_image, image_2, image_3, and image_4')
    }
    
    // Use V2 API with multipart form data
    console.log('Using Fashion Lab API V2 with image inputs')
    
    const formData = new FormData()
    
    // Convert base64 images to blobs and add to form data
    const faceBlob = base64ToBlob(body.face_image)
    const image2Blob = base64ToBlob(body.image_2)
    const image3Blob = base64ToBlob(body.image_3)
    const image4Blob = base64ToBlob(body.image_4)
    
    formData.append('face', faceBlob, 'face.jpg')
    formData.append('image_2', image2Blob, 'image_2.jpg')
    formData.append('image_3', image3Blob, 'image_3.jpg')
    formData.append('image_4', image4Blob, 'image_4.jpg')
    formData.append('prompt', body.prompt)
    
    // Add timeout to prevent 504 errors
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), 25000) // 25 second timeout
    
    let response: Response
    
    try {
      response = await fetch('https://fashionlab.notfirst.rodeo/generate-image-v2', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${fashionLabToken}`,
        },
        body: formData,
        signal: controller.signal,
      })
    } catch (error) {
      if (error.name === 'AbortError') {
        console.log('Request timed out, returning queued response')
        // Return a queued response instead of failing
        return new Response(
          JSON.stringify({
            queue_id: `timeout-${Date.now()}`,
            status: 'processing',
            images: [],
            error: null
          }),
          { 
            headers: { 
              ...corsHeaders,
              'Content-Type': 'application/json'
            } 
          }
        )
      }
      throw error
    } finally {
      clearTimeout(timeoutId)
    }
    
    if (!response.ok) {
      const errorText = await response.text()
      console.error('Fashion Lab API error:', errorText)
      throw new Error(`Fashion Lab API error: ${response.status} ${errorText}`)
    }
    
    const result = await response.json()
    console.log('Fashion Lab API response:', result)
    
    // Transform the response to match our expected format
    let transformedResponse: any = {
      queue_id: result.queue_id,
      status: result.status === 'pending' ? 'processing' : result.status,
      images: [],
      error: result.error,
    }
    
    // If completed, extract images from data.images
    if (result.status === 'completed' && result.data?.images) {
      transformedResponse.images = result.data.images.map((imageData: any) => {
        if (typeof imageData === 'string') {
          // If it's already a full URL, return as-is
          if (imageData.startsWith('http')) {
            return imageData
          } 
          // If it's a data URI, return as-is
          else if (imageData.startsWith('data:image')) {
            return imageData
          } 
          // If it looks like a path, convert to full URL
          else if (imageData.includes('/')) {
            return `https://fashionlab.notfirst.rodeo${imageData}`
          } 
          // Otherwise assume it's base64 without prefix
          else {
            return `data:image/png;base64,${imageData}`
          }
        } else if (imageData && typeof imageData === 'object') {
          // Handle object format from the API
          if (imageData.type === 'base64' && imageData.data) {
            return `data:image/png;base64,${imageData.data}`
          } else if (imageData.type === 's3_url' && imageData.data) {
            return imageData.data
          } else if (imageData.filename && imageData.data) {
            // Handle the format from the handler.py we saw
            return `data:image/png;base64,${imageData.data}`
          }
        }
        return imageData
      })
      transformedResponse.status = 'completed'
    }
    
    return new Response(
      JSON.stringify(transformedResponse),
      { 
        headers: { 
          ...corsHeaders,
          'Content-Type': 'application/json'
        } 
      }
    )
  } catch (error: unknown) {
    console.error('Error in generate-images function:', error)
    
    const errorMessage = error instanceof Error ? error.message : 'Failed to generate images'
    
    return new Response(
      JSON.stringify({ error: errorMessage }),
      { 
        status: 500,
        headers: { 
          ...corsHeaders,
          'Content-Type': 'application/json'
        } 
      }
    )
  }
})

// Helper function to convert base64 to blob
function base64ToBlob(base64: string): Blob {
  // Remove data URL prefix if present
  const base64Data = base64.replace(/^data:image\/\w+;base64,/, '')
  
  // Convert base64 to binary
  const binaryString = atob(base64Data)
  const bytes = new Uint8Array(binaryString.length)
  
  for (let i = 0; i < binaryString.length; i++) {
    bytes[i] = binaryString.charCodeAt(i)
  }
  
  // Return as blob with JPEG mime type
  return new Blob([bytes], { type: 'image/jpeg' })
}