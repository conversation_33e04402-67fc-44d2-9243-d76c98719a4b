{"permissions": {"allow": ["Bash(gh pr:*)", "mcp__supabase__list_tables", "mcp__supabase__execute_sql", "Bash(git add:*)", "Bash(git commit:*)", "mcp__linear__linear_getIssues", "mcp__linear__linear_searchIssues", "mcp__linear__linear_createIssue", "mcp__linear__linear_getTeams", "mcp__linear__linear_getIssueById", "Bash(find:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(npm run typecheck:*)", "Bash(npm run type-check:*)", "Bash(supabase migration:*)", "Bash(supabase db reset:*)", "Bash(supabase status:*)", "Bash(supabase db:*)", "Bash(psql:*)", "<PERSON><PERSON>(mv:*)", "Bash(ls:*)", "Bash(rm:*)", "Bash(PGPASSWORD=postgres psql -h localhost -p 54322 -U postgres -d postgres -f /Users/<USER>/Documents/fashionlab-v1/supabase/seed.sql)", "Bash(npm run setup:test-data:*)", "Bash(npm run dev:*)", "Bash(npm run lint)", "Bash(npm run lint:*)", "<PERSON><PERSON>(docker exec:*)", "Bash(npm run supabase:mcp:help:*)", "Bash(supabase link:*)", "mcp__postgres__query", "Bash(supabase projects:*)", "<PERSON><PERSON>(env)"], "deny": []}}